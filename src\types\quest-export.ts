// Quest Export Types for C# Gameserver
// Cấu trúc dữ liệu export tương thích với gameserver C#

/**
 * Enum cho các loại yêu cầu quest
 */
export enum QuestRequirementType {
  ITEM = 'item',
  LEVEL = 'level',
  JOB = 'job',
  JOB_LEVEL = 'job_level',
  ABILITY_POINT = 'ability_point',
  FACTION = 'faction',
  GUILD = 'guild'
}

/**
 * Enum cho các loại phần thưởng quest
 */
export enum QuestRewardType {
  ITEM = 'item',
  EXPERIENCE = 'experience',
  GOLD = 'gold',
  SKILL_POINT = 'skill_point'
}

/**
 * Enum cho trạng thái quest
 */
export enum QuestStatus {
  AVAILABLE = 'available',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

/**
 * Cấu trúc yêu cầu quest mở rộng
 */
export interface QuestRequirement {
  type: QuestRequirementType;
  value: number;
  description?: string;
  // Cho item requirements
  itemId?: number;
  itemAmount?: number;
  mapId?: number;
  coordsX?: number;
  coordsY?: number;
  coordsZ?: number;
  // Cho job requirements
  jobId?: number;
  // Cho faction requirements
  factionId?: number;
  // Cho guild requirements
  guildId?: number;
  guildRank?: number;
}

/**
 * Cấu trúc phần thưởng quest mở rộng
 */
export interface QuestReward {
  type: QuestRewardType;
  value: number;
  description?: string;
  // Cho item rewards
  itemId?: number;
  itemAmount?: number;
}

/**
 * Thông tin NPC
 */
export interface NpcInfo {
  npcId: number;
  mapId: number;
  coordsX: number;
  coordsY: number;
  coordsZ: number;
  unknown1?: number;
}

/**
 * Dialog prompts
 */
export interface QuestDialogs {
  accept: string[];
  refuse: string[];
  welcomeAccept: string[];
  welcomeRefuse: string[];
}

/**
 * Quest Stage cho export
 */
export interface ExportQuestStage {
  stageId: number;
  content: string;
  npc: NpcInfo;
  requirements: QuestRequirement[];
  rewards?: QuestReward[];
  dialogs: {
    conditionMatch: string[];
    conditionNoMatch: string[];
  };
}

/**
 * Quest chính cho export
 */
export interface ExportQuest {
  questId: number;
  questName: string;
  questLevel: number;
  description?: string;
  questType: string;

  // Yêu cầu để nhận quest
  acceptRequirements: QuestRequirement[];

  // Yêu cầu để hoàn thành quest
  completionRequirements: QuestRequirement[];

  // Phần thưởng quest
  rewards: QuestReward[];

  // NPC quest giver
  questGiver: NpcInfo;

  // Dialog
  dialogs: QuestDialogs;

  // Các stage của quest
  stages: ExportQuestStage[];

  // Metadata
  isSpecialQuest: boolean;
  category?: string;

  // Footer extend (nếu có)
  footerExtend?: string;

  // Unknown fields từ dữ liệu gốc (để tương thích)
  unknownFields: {
    unknown1: number;
    unknown2: number;
    unknown3: number;
    unknown4: number;
    unknown5: number;
    unknown6: number;
    unknown7: number;
    unknown8: number;
    unknown9: number;
    unknown10: number;
    unknown11: number;
    unknown12?: number;
    unknown13?: number;
    unknown14?: number;
    unknown15?: number;
    unknown16?: number;
    unknown17?: number;
    unknown18?: number;
    unknown19?: number;
    unknown20?: number;
  };
}

/**
 * Cấu trúc export data hoàn chỉnh
 */
export interface QuestExportData {
  version: string;
  exportDate: string;
  totalQuests: number;
  quests: ExportQuest[];
  metadata: {
    originalSign: string;
    originalSignEx: string;
    exportFormat: 'gameserver_csharp';
  };
}

/**
 * Configuration cho export
 */
export interface QuestExportConfig {
  includeUnknownFields: boolean;
  includeDialogs: boolean;
  includeCoordinates: boolean;
  minifyOutput: boolean;
  validateRequirements: boolean;
}

/**
 * Response cho export API
 */
export interface QuestExportResponse {
  success: boolean;
  message: string;
  data?: QuestExportData;
  fileData?: string; // Base64 encoded
  fileName?: string;
}

/**
 * Request cho export API
 */
export interface QuestExportRequest {
  questIds?: number[]; // Nếu không có thì export tất cả
  config: QuestExportConfig;
  format: 'json' | 'xml' | 'binary';
}

/**
 * Validation result
 */
export interface QuestValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  questId: number;
}

/**
 * Export statistics
 */
export interface QuestExportStats {
  totalQuests: number;
  exportedQuests: number;
  skippedQuests: number;
  errors: number;
  warnings: number;
  processingTime: number;
}