// Quest Exporter - <PERSON><PERSON><PERSON><PERSON> đổi từ YBQ data sang format gameserver C#
import {
  Quest,
  QuestStage,
  QuestRequiredItem,
  QuestRewardItem,
  YbqData
} from '@/types/ybq';
import {
  ExportQuest,
  ExportQuestStage,
  QuestRequirement,
  QuestReward,
  QuestRequirementType,
  QuestRewardType,
  NpcInfo,
  QuestDialogs,
  QuestExportData,
  QuestExportConfig,
  QuestValidationResult,
  QuestExportStats
} from '@/types/quest-export';

/**
 * Quest Exporter Class
 */
export class QuestExporter {
  private config: QuestExportConfig;
  private stats: QuestExportStats;

  constructor(config: QuestExportConfig) {
    this.config = config;
    this.stats = {
      totalQuests: 0,
      exportedQuests: 0,
      skippedQuests: 0,
      errors: 0,
      warnings: 0,
      processingTime: 0
    };
  }

  /**
   * Export YBQ data sang format gameserver
   */
  public exportQuestData(ybqData: YbqData, questIds?: number[]): QuestExportData {
    const startTime = Date.now();

    try {
      const questsToExport = questIds
        ? Object.values(ybqData.quests).filter(q => questIds.includes(q.questID.value))
        : Object.values(ybqData.quests);

      this.stats.totalQuests = questsToExport.length;

      const exportedQuests: ExportQuest[] = [];

      for (const quest of questsToExport) {
        try {
          const exportedQuest = this.convertQuest(quest);

          if (this.config.validateRequirements) {
            const validation = this.validateQuest(exportedQuest);
            if (!validation.isValid) {
              console.warn(`Quest ${quest.questID.value} validation failed:`, validation.errors);
              this.stats.warnings += validation.warnings.length;
              this.stats.errors += validation.errors.length;
            }
          }

          exportedQuests.push(exportedQuest);
          this.stats.exportedQuests++;
        } catch (error) {
          console.error(`Error converting quest ${quest.questID.value}:`, error);
          this.stats.errors++;
          this.stats.skippedQuests++;
        }
      }

      const exportData: QuestExportData = {
        version: '1.0.0',
        exportDate: new Date().toISOString(),
        totalQuests: exportedQuests.length,
        quests: exportedQuests,
        metadata: {
          originalSign: ybqData.sign,
          originalSignEx: ybqData.signEx,
          exportFormat: 'gameserver_csharp'
        }
      };

      this.stats.processingTime = Date.now() - startTime;
      return exportData;

    } catch (error) {
      this.stats.processingTime = Date.now() - startTime;
      throw new Error(`Export failed: ${error}`);
    }
  }

  /**
   * Chuyển đổi Quest sang ExportQuest
   */
  private convertQuest(quest: Quest): ExportQuest {
    const exportQuest: ExportQuest = {
      questId: quest.questID.value,
      questName: quest.questName.value,
      questLevel: quest.questLevel.value,
      description: quest.questName.value, // Sử dụng name làm description tạm thời
      questType: 'none', // Mặc định là main quest, có thể thêm logic để xác định

      // Yêu cầu để nhận quest (hiện tại chưa có trong data gốc)
      acceptRequirements: this.extractAcceptRequirements(quest),

      // Yêu cầu để hoàn thành quest
      completionRequirements: this.convertRequiredItems(quest.requiredItems),

      // Phần thưởng quest
      rewards: this.convertRewardItems(quest.rewardItems),

      // NPC quest giver
      questGiver: this.convertNpcInfo(quest),

      // Dialog
      dialogs: this.convertDialogs(quest),

      // Các stage của quest
      stages: this.convertQuestStages(quest.questStages),

      // Metadata
      isSpecialQuest: quest.unknown10.value === 16,
      category: this.determineQuestCategory(quest),

      // Footer extend
      footerExtend: quest.footerExtend?.value || '',

      // Unknown fields để tương thích
      unknownFields: {
        unknown1: quest.unknown1.value,
        unknown2: quest.unknown2.value,
        unknown3: quest.unknown3.value,
        unknown4: quest.unknown4.value,
        unknown5: quest.unknown5.value,
        unknown6: quest.unknown6.value,
        unknown7: quest.unknown7.value,
        unknown8: quest.unknown8.value,
        unknown9: quest.unknown9.value,
        unknown10: quest.unknown10.value,
        unknown11: quest.unknown11.value,
        unknown12: quest.unknown12?.value,
        unknown13: quest.unknown13?.value,
        unknown14: quest.unknown14?.value,
        unknown15: quest.unknown15?.value,
        unknown16: quest.unknown16?.value,
        unknown17: quest.unknown17?.value,
        unknown18: quest.unknown18?.value,
        unknown19: quest.unknown19?.value,
        unknown20: quest.unknown20?.value
      }
    };

    return exportQuest;
  }

  /**
   * Trích xuất yêu cầu để nhận quest (từ unknown fields hoặc logic tùy chỉnh)
   */
  private extractAcceptRequirements(quest: Quest): QuestRequirement[] {
    const requirements: QuestRequirement[] = [];

    // Yêu cầu level từ questLevel
    if (quest.questLevel.value > 0) {
      requirements.push({
        type: QuestRequirementType.LEVEL,
        value: quest.questLevel.value,
        description: `Yêu cầu level tối thiểu ${quest.questLevel.value}`
      });
    }

    // Có thể thêm logic để trích xuất từ unknown fields
    // Ví dụ: unknown3 có thể là job requirement, unknown4 có thể là faction requirement

    return requirements;
  }

  /**
   * Chuyển đổi required items sang QuestRequirement[]
   */
  private convertRequiredItems(requiredItems: QuestRequiredItem[]): QuestRequirement[] {
    return requiredItems.map(item => ({
      type: QuestRequirementType.ITEM,
      value: item.itemAmount.value,
      itemId: item.itemID.value,
      itemAmount: item.itemAmount.value,
      mapId: item.mapID.value,
      coordsX: item.coordsX.value,
      coordsY: item.coordsY.value,
      coordsZ: item.coordsZ.value,
      description: `Cần ${item.itemAmount.value} item ID ${item.itemID.value}`
    }));
  }

  /**
   * Chuyển đổi reward items sang QuestReward[]
   */
  private convertRewardItems(rewardItems: QuestRewardItem[]): QuestReward[] {
    return rewardItems.map(item => ({
      type: QuestRewardType.ITEM,
      value: item.itemAmount.value,
      itemId: item.itemID.value,
      itemAmount: item.itemAmount.value,
      description: `Nhận ${item.itemAmount.value} item ID ${item.itemID.value}`
    }));
  }

  /**
   * Chuyển đổi thông tin NPC
   */
  private convertNpcInfo(quest: Quest): NpcInfo {
    return {
      npcId: quest.npcID.value,
      mapId: quest.npcCoords.mapID.value,
      coordsX: quest.npcCoords.coordsX.value,
      coordsY: quest.npcCoords.coordsY.value,
      coordsZ: quest.npcCoords.coordsZ.value,
      unknown1: quest.npcUnknown1.value
    };
  }

  /**
   * Chuyển đổi dialog
   */
  private convertDialogs(quest: Quest): QuestDialogs {
    const dialogs: QuestDialogs = {
      accept: [],
      refuse: [],
      welcomeAccept: [],
      welcomeRefuse: []
    };

    // Accept dialogs
    if (quest.questAccept0?.value) dialogs.accept.push(quest.questAccept0.value);
    if (quest.questAccept1?.value) dialogs.accept.push(quest.questAccept1.value);
    if (quest.questAccept2?.value) dialogs.accept.push(quest.questAccept2.value);

    // Refuse dialogs
    if (quest.questRefuse1?.value) dialogs.refuse.push(quest.questRefuse1.value);
    if (quest.questRefuse2?.value) dialogs.refuse.push(quest.questRefuse2.value);

    // Welcome accept dialogs
    if (quest.welcomeAcceptPrompt1?.value) dialogs.welcomeAccept.push(quest.welcomeAcceptPrompt1.value);
    if (quest.welcomeAcceptPrompt2?.value) dialogs.welcomeAccept.push(quest.welcomeAcceptPrompt2.value);
    if (quest.welcomeAcceptPrompt3?.value) dialogs.welcomeAccept.push(quest.welcomeAcceptPrompt3.value);
    if (quest.welcomeAcceptPrompt4?.value) dialogs.welcomeAccept.push(quest.welcomeAcceptPrompt4.value);
    if (quest.welcomeAcceptPrompt5?.value) dialogs.welcomeAccept.push(quest.welcomeAcceptPrompt5.value);

    // Welcome refuse dialogs
    if (quest.welcomeRefusePrompt1?.value) dialogs.welcomeRefuse.push(quest.welcomeRefusePrompt1.value);
    if (quest.welcomeRefusePrompt2?.value) dialogs.welcomeRefuse.push(quest.welcomeRefusePrompt2.value);
    if (quest.welcomeRefusePrompt3?.value) dialogs.welcomeRefuse.push(quest.welcomeRefusePrompt3.value);
    if (quest.welcomeRefusePrompt4?.value) dialogs.welcomeRefuse.push(quest.welcomeRefusePrompt4.value);
    if (quest.welcomeRefusePrompt5?.value) dialogs.welcomeRefuse.push(quest.welcomeRefusePrompt5.value);

    return dialogs;
  }

  /**
   * Chuyển đổi quest stages
   */
  private convertQuestStages(questStages: QuestStage[]): ExportQuestStage[] {
    return questStages.map((stage, index) => ({
      stageId: index + 1,
      content: stage.content.value,
      npc: {
        npcId: stage.npcID.value,
        mapId: stage.npcMapID.value,
        coordsX: stage.npcCoordsX.value,
        coordsY: stage.npcCoordsY.value,
        coordsZ: stage.npcCoordsZ.value,
        unknown1: stage.npcUnknown1.value
      },
      requirements: this.convertRequiredItems(stage.requiredItems),
      dialogs: {
        conditionMatch: [
          stage.conditionMatchPrompt1?.value,
          stage.conditionMatchPrompt2?.value,
          stage.conditionMatchPrompt3?.value,
          stage.conditionMatchPrompt4?.value,
          stage.conditionMatchPrompt5?.value
        ].filter(Boolean) as string[],
        conditionNoMatch: [
          stage.conditionNoMatchPrompt1?.value,
          stage.conditionNoMatchPrompt2?.value,
          stage.conditionNoMatchPrompt3?.value,
          stage.conditionNoMatchPrompt4?.value,
          stage.conditionNoMatchPrompt5?.value
        ].filter(Boolean) as string[]
      }
    }));
  }

  /**
   * Xác định category của quest dựa trên unknown fields hoặc logic
   */
  private determineQuestCategory(quest: Quest): string {
    // Logic để xác định category dựa trên unknown fields
    if (quest.unknown10.value === 16) {
      return 'special';
    }

    if (quest.questLevel.value >= 50) {
      return 'high_level';
    }

    if (quest.questStages.length > 3) {
      return 'multi_stage';
    }

    return 'normal';
  }

  /**
   * Validate quest data
   */
  private validateQuest(quest: ExportQuest): QuestValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate basic fields
    if (!quest.questName || quest.questName.trim() === '') {
      errors.push('Quest name is required');
    }

    if (quest.questLevel < 0) {
      errors.push('Quest level cannot be negative');
    }

    if (quest.questId <= 0) {
      errors.push('Quest ID must be positive');
    }

    // Validate NPC info
    if (quest.questGiver.npcId <= 0) {
      warnings.push('Quest giver NPC ID is not set');
    }

    // Validate requirements
    for (const req of quest.completionRequirements) {
      if (req.type === QuestRequirementType.ITEM) {
        if (!req.itemId || req.itemId <= 0) {
          errors.push(`Invalid item ID in requirement: ${req.itemId}`);
        }
        if (!req.itemAmount || req.itemAmount <= 0) {
          errors.push(`Invalid item amount in requirement: ${req.itemAmount}`);
        }
      }
    }

    // Validate rewards
    for (const reward of quest.rewards) {
      if (reward.type === QuestRewardType.ITEM) {
        if (!reward.itemId || reward.itemId <= 0) {
          errors.push(`Invalid item ID in reward: ${reward.itemId}`);
        }
        if (!reward.itemAmount || reward.itemAmount <= 0) {
          errors.push(`Invalid item amount in reward: ${reward.itemAmount}`);
        }
      }
    }

    // Validate stages
    for (let i = 0; i < quest.stages.length; i++) {
      const stage = quest.stages[i];
      if (!stage.content || stage.content.trim() === '') {
        warnings.push(`Stage ${i + 1} has empty content`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      questId: quest.questId
    };
  }

  /**
   * Get export statistics
   */
  public getStats(): QuestExportStats {
    return { ...this.stats };
  }

  /**
   * Reset statistics
   */
  public resetStats(): void {
    this.stats = {
      totalQuests: 0,
      exportedQuests: 0,
      skippedQuests: 0,
      errors: 0,
      warnings: 0,
      processingTime: 0
    };
  }

  /**
   * Export to JSON string
   */
  public exportToJson(exportData: QuestExportData): string {
    if (this.config.minifyOutput) {
      return JSON.stringify(exportData);
    } else {
      return JSON.stringify(exportData, null, 2);
    }
  }

  /**
   * Export to XML string
   */
  public exportToXml(exportData: QuestExportData): string {
    // Implement XML export logic
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += '<QuestExportData>\n';
    xml += `  <Version>${exportData.version}</Version>\n`;
    xml += `  <ExportDate>${exportData.exportDate}</ExportDate>\n`;
    xml += `  <TotalQuests>${exportData.totalQuests}</TotalQuests>\n`;
    xml += '  <Metadata>\n';
    xml += `    <OriginalSign>${exportData.metadata.originalSign}</OriginalSign>\n`;
    xml += `    <OriginalSignEx>${exportData.metadata.originalSignEx}</OriginalSignEx>\n`;
    xml += `    <ExportFormat>${exportData.metadata.exportFormat}</ExportFormat>\n`;
    xml += '  </Metadata>\n';
    xml += '  <Quests>\n';

    for (const quest of exportData.quests) {
      xml += this.questToXml(quest, '    ');
    }

    xml += '  </Quests>\n';
    xml += '</QuestExportData>';

    return xml;
  }

  /**
   * Convert single quest to XML
   */
  private questToXml(quest: ExportQuest, indent: string): string {
    let xml = `${indent}<Quest>\n`;
    xml += `${indent}  <QuestId>${quest.questId}</QuestId>\n`;
    xml += `${indent}  <QuestName><![CDATA[${quest.questName}]]></QuestName>\n`;
    xml += `${indent}  <QuestLevel>${quest.questLevel}</QuestLevel>\n`;
    xml += `${indent}  <IsSpecialQuest>${quest.isSpecialQuest}</IsSpecialQuest>\n`;

    // Add more XML conversion logic as needed

    xml += `${indent}</Quest>\n`;
    return xml;
  }
}